import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { openaiService } from '$lib/tools/openai';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { question } = await request.json();

		if (!question || typeof question !== 'string') {
			return json(
				{ error: 'Question is required and must be a string' },
				{ status: 400 }
			);
		}

		if (question.trim().length === 0) {
			return json(
				{ error: 'Question cannot be empty' },
				{ status: 400 }
			);
		}

		const response = await openaiService.generateSmartOverview(question.trim());

		return json({
			success: true,
			response,
			timestamp: new Date().toISOString()
		});

	} catch (error) {
		console.error('Error in OpenAI endpoint:', error);

		return json(
			{
				error: 'Failed to generate response',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
