<script lang="ts">
	import { onMount } from 'svelte';
	import { gsap } from 'gsap';

	import BlackRasengan from '$lib/components/black-rasengan.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import * as Accordion from '$lib/components/ui/accordion/index.js';

	import { match } from 'ts-pattern';
	import { cn } from '$lib/utils';

	import SmartOverview from '$lib/components/about-steps/smart-overview.svelte';
	import ExperienceRoadmap from '$lib/components/about-steps/experience-roadmap.svelte';
	import ProjectsList from '$lib/components/about-steps/projects-list.svelte';
	import { globals } from '$lib/states/global.state.svelte';

	const tabOptions = [
		{
			icon: 'streamline-flex:ai-scanner-robot-solid',
			label: globals.texts().homePage.tabMenu.options.smartOverview,
			value: 'smart-overview'
		},
		{
			icon: 'uil:brain',
			label: globals.texts().homePage.tabMenu.options.experience,
			value: 'experience'
		},
		{
			icon: 'ix:project-configuration',
			label: globals.texts().homePage.tabMenu.options.projects,
			value: 'projects'
		}
	];

	let isContactOpen = $state(false);
	let isAboutOpen = $state(false);
	let isSettingsOpen = $state(false);

	let dialogTabState = $state('smart-overview');

	let isAnimationReady = $state(false);
	let backgroundContainer: HTMLElement;
	let titleElement: HTMLElement;

	let displayText = $state('TARCISIO ALMEIDA');
	const finalText = 'TARCISIO ALMEIDA';
	const chars = 'ABCDEFHIREMEMNOPQRSTUVWXYZ0123456789!@#$%&*';
	let isScrambling = $state(false);
	let scrambleInterval: number;

	function startScramble(duration: number = 3.5, delay: number = 0) {
		if (isScrambling) return;
		isScrambling = true;
		const startTime = performance.now() + delay * 1000;
		if (scrambleInterval) clearInterval(scrambleInterval);

		scrambleInterval = setInterval(() => {
			const elapsed = (performance.now() - startTime) / 1000;
			if (elapsed < 0) return;

			const progress = Math.min(elapsed / duration, 1);
			const revealCount = Math.floor(progress * finalText.length);
			const newChars: string[] = finalText.split('').map((char, index) => {
				if (index < revealCount) return char;
				if (char === ' ') return ' ';
				const mix = Math.random() * (1 - progress);
				return `<span class='scrambled-char' style='opacity:${1 - progress};'>${
					chars[Math.floor(mix * chars.length)]
				}</span>`;
			});

			displayText = newChars.join('');
			if (progress >= 1) {
				clearInterval(scrambleInterval);
				displayText = finalText;
				isScrambling = false;
			}
		}, 80);
	}

	function handleMouseEnter() {
		startScramble(2.0);
	}

	onMount(() => {
		if (!backgroundContainer) return;
		const ttl = gsap.timeline({
			onComplete: () => {
				isAnimationReady = true;
			}
		});

		ttl.fromTo(
			backgroundContainer,
			{ opacity: 0, filter: 'blur(10px)', scale: 1.02 },
			{ opacity: 1, filter: 'blur(0px)', scale: 1, duration: 3.5, ease: 'power2.out' }
		);

		ttl.call(() => startScramble(3.5), [], 0.3);
	});
</script>

<svelte:head>
	<title>TARCS</title>
</svelte:head>

<section class="animated-bg font-body h-dvh min-h-dvh w-full overflow-hidden p-10 text-gray-600/80">
	<div
		bind:this={backgroundContainer}
		class="relative z-10 size-full rounded-2xl border border-white/20 bg-white/10 p-8 shadow-2xl backdrop-blur-md"
	>
		<div class="flex size-full flex-col items-center justify-center">
			<div class="h-[290px] w-[250px] overflow-hidden rounded-xl shadow-2xl">
				<img src="https://ik.imagekit.io/og7loqgh2/tarcs.png?updatedAt=1751378855852" alt="me" />
			</div>

			<h1
				bind:this={titleElement}
				class="font-heading mt-5 cursor-pointer text-3xl font-extrabold tracking-tight"
				onmouseenter={handleMouseEnter}
			>
				{@html displayText}
			</h1>

			<Icon icon="material-symbols:code-rounded" class="mt-1 text-2xl text-gray-500" />

			<p class="mt-1 text-center text-xl font-semibold">
				{globals.texts().homePage.jobTitle}
			</p>

			<div class="mt-7 flex items-center gap-2">
				<button
					class="font-heading relative h-[40px] w-fit cursor-pointer overflow-hidden rounded-md bg-white/10 px-4 font-bold shadow-2xl backdrop-blur-md before:absolute before:top-0 before:left-0 before:h-full before:w-[3px] before:rounded-l-md before:bg-gray-500/90"
					onclick={() => (isContactOpen = true)}
				>
					{globals.texts().homePage.buttons.contact}
				</button>
				<button
					class="font-heading relative h-[40px] w-fit cursor-pointer overflow-hidden rounded-md bg-white/10 px-4 font-bold shadow-2xl backdrop-blur-md before:absolute before:top-0 before:left-0 before:h-full before:w-[3px] before:rounded-l-md before:bg-gray-500/30"
					onclick={() => (isAboutOpen = true)}
				>
					{globals.texts().homePage.buttons.about}
				</button>
			</div>
		</div>

		<Popover.Root>
			<Popover.Trigger class="absolute top-0 right-0 mt-2 mr-4 cursor-pointer">
				<Icon icon="solar:menu-dots-bold-duotone" class="text-3xl" />
			</Popover.Trigger>
			<Popover.Content class="min-w-[250px] border-0 bg-white/30 p-4 shadow-none backdrop-blur-md">
				<Accordion.Root type="single">
					<Accordion.Item value="language" class="border-b border-white/20 pb-2">
						<Accordion.Trigger
							class="flex w-full items-center justify-between py-2 text-left font-medium text-gray-800"
						>
							{globals.texts().homePage.langMenu.title}
						</Accordion.Trigger>
						<Accordion.Content class="space-y-2 pt-2">
							<button
								class={cn(
									'block w-full rounded px-2 py-1 text-left text-gray-700 hover:bg-white/20',
									{
										'bg-white/20': globals.ui.currentLang === 'pt'
									}
								)}
								onclick={() => globals.setLang('pt')}
							>
								{globals.texts().homePage.langMenu.options.pt}
							</button>
							<button
								class={cn(
									'block w-full rounded px-2 py-1 text-left text-gray-700 hover:bg-white/20',
									{
										'bg-white/20': globals.ui.currentLang === 'en'
									}
								)}
								onclick={() => globals.setLang('en')}
							>
								{globals.texts().homePage.langMenu.options.en}
							</button>
							<button
								class={cn(
									'block w-full rounded px-2 py-1 text-left text-gray-700 hover:bg-white/20',
									{
										'bg-white/20': globals.ui.currentLang === 'ger'
									}
								)}
								onclick={() => globals.setLang('ger')}
							>
								{globals.texts().homePage.langMenu.options.ger}
							</button>
						</Accordion.Content>
					</Accordion.Item>

					<Accordion.Item value="sound">
						<Accordion.Trigger
							class="flex w-full items-center justify-between py-2 text-left font-medium text-gray-800"
						>
							{globals.texts().homePage.soundMenu.title}
						</Accordion.Trigger>
						<Accordion.Content class="space-y-2 pt-2">
							<button
								class="block w-full rounded px-2 py-1 text-left text-gray-700 hover:bg-white/20"
							>
								{globals.texts().homePage.soundMenu.options.off}
							</button>
							<button
								class="block w-full rounded px-2 py-1 text-left text-gray-700 hover:bg-white/20"
							>
								{globals.texts().homePage.soundMenu.options.on}
							</button>
						</Accordion.Content>
					</Accordion.Item>
				</Accordion.Root>
			</Popover.Content>
		</Popover.Root>

		<img
			src="https://ik.imagekit.io/og7loqgh2/tarcs-logo.png?updatedAt=1751385196900"
			class="absolute top-0 left-0 m-2 w-12 opacity-80"
			alt="logo"
		/>

		<div class="absolute right-4 bottom-4">
			<BlackRasengan />
		</div>
	</div>
</section>

<Dialog.Root open={isAboutOpen} onOpenChange={(open) => (isAboutOpen = open)}>
	<Dialog.Overlay class="bg-black/30 backdrop-blur-md" />
	<Dialog.Content class="!max-w-3xl border-0 bg-gradient-to-br from-white/95 to-gray-200/90">
		<div class="flex items-center gap-3">
			{#each tabOptions as option}
				<button
					class={cn(
						'relative flex w-fit cursor-pointer items-center gap-2 overflow-hidden rounded-md border border-white/20 bg-white/10 px-4 py-2 text-gray-800 shadow-2xl after:absolute after:top-0 after:left-0 after:h-full after:w-[4px] after:rounded-l-md after:transition-colors hover:after:bg-gray-500/90',
						match(option.value === dialogTabState)
							.with(true, () => 'after:bg-gray-500/90')
							.with(false, () => 'opacity-50 after:bg-gray-500/10')
							.exhaustive()
					)}
					onclick={() => (dialogTabState = option.value)}
				>
					<Icon icon={option.icon} class="text-xl text-gray-800" />
					<span>{option.label}</span>
				</button>
			{/each}
		</div>

		<div>
			{#if dialogTabState === 'smart-overview'}
				<SmartOverview />
			{:else if dialogTabState === 'experience'}
				<ExperienceRoadmap />
			{:else if dialogTabState === 'projects'}
				<ProjectsList />
			{/if}
		</div>
	</Dialog.Content>
</Dialog.Root>

<style>
	.animated-bg {
		position: relative;
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	}

	.animated-bg::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background:
			radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
			radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
			radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.25) 0%, transparent 50%),
			radial-gradient(circle at 90% 70%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
		animation: float-bubbles 20s ease-in-out infinite;
	}

	.animated-bg::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			45deg,
			rgba(255, 255, 255, 0.1) 0%,
			rgba(240, 240, 240, 0.05) 25%,
			rgba(255, 255, 255, 0.08) 50%,
			rgba(245, 245, 245, 0.03) 75%,
			rgba(255, 255, 255, 0.12) 100%
		);
		background-size: 400% 400%;
		animation: gradient-shift 15s ease infinite;
	}

	@keyframes float-bubbles {
		0%,
		100% {
			transform: translateY(0px) rotate(0deg);
			opacity: 1;
		}
		33% {
			transform: translateY(-20px) rotate(120deg);
			opacity: 0.8;
		}
		66% {
			transform: translateY(-10px) rotate(240deg);
			opacity: 0.9;
		}
	}

	@keyframes gradient-shift {
		0% {
			background-position: 0% 50%;
		}
		50% {
			background-position: 100% 50%;
		}
		100% {
			background-position: 0% 50%;
		}
	}
</style>
