import { OPENAI_API_KEY } from '$env/static/private';

export interface OpenAIMessage {
	role: 'system' | 'user' | 'assistant';
	content: string;
}

export interface OpenAIRequest {
	model: string;
	messages: OpenAIMessage[];
	temperature?: number;
	max_tokens?: number;
	stream?: boolean;
}

export interface OpenAIResponse {
	id: string;
	object: string;
	created: number;
	model: string;
	choices: {
		index: number;
		message: {
			role: string;
			content: string;
		};
		finish_reason: string;
	}[];
	usage: {
		prompt_tokens: number;
		completion_tokens: number;
		total_tokens: number;
	};
}

export class OpenAIService {
	private apiKey: string;
	private baseUrl: string = 'https://api.openai.com/v1';

	constructor(apiKey?: string) {
		this.apiKey = apiKey || OPENAI_API_KEY;
		if (!this.apiKey) {
			throw new Error('OpenAI API key is required');
		}
	}

	async createChatCompletion(request: OpenAIRequest): Promise<OpenAIResponse> {
		const response = await fetch(`${this.baseUrl}/chat/completions`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${this.apiKey}`
			},
			body: JSON.stringify(request)
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`OpenAI API error: ${response.status} - ${error}`);
		}

		return response.json();
	}

	private detectLanguage(text: string): 'pt' | 'en' | 'de' {
		// Palavras-chave para detecção de idioma
		const ptKeywords = ['você', 'como', 'que', 'para', 'com', 'uma', 'seu', 'sua', 'sobre', 'trabalho', 'experiência', 'projeto'];
		const enKeywords = ['you', 'how', 'what', 'for', 'with', 'your', 'about', 'work', 'experience', 'project', 'can', 'tell'];
		const deKeywords = ['sie', 'wie', 'was', 'für', 'mit', 'ihr', 'ihre', 'über', 'arbeit', 'erfahrung', 'projekt', 'können'];

		const lowerText = text.toLowerCase();

		const ptScore = ptKeywords.filter(word => lowerText.includes(word)).length;
		const enScore = enKeywords.filter(word => lowerText.includes(word)).length;
		const deScore = deKeywords.filter(word => lowerText.includes(word)).length;

		if (deScore > ptScore && deScore > enScore) return 'de';
		if (enScore > ptScore && enScore > deScore) return 'en';
		return 'pt'; // Default para português
	}

	private getLanguageInstructions(language: 'pt' | 'en' | 'de'): string {
		const instructions = {
			pt: `
## 🌍 INSTRUÇÃO CRÍTICA DE IDIOMA:
- RESPONDA EXCLUSIVAMENTE EM PORTUGUÊS BRASILEIRO
- Use terminologia técnica em português
- Mantenha naturalidade e fluidez no idioma
- Adapte expressões técnicas para o contexto brasileiro`,

			en: `
## 🌍 CRITICAL LANGUAGE INSTRUCTION:
- RESPOND EXCLUSIVELY IN ENGLISH
- Use technical terminology in English
- Maintain natural fluency in the language
- Adapt technical expressions to English context`,

			de: `
## 🌍 KRITISCHE SPRACHANWEISUNG:
- ANTWORTEN SIE AUSSCHLIESSLICH AUF DEUTSCH
- Verwenden Sie technische Terminologie auf Deutsch
- Behalten Sie natürliche Sprachgewandtheit bei
- Passen Sie technische Ausdrücke an den deutschen Kontext an`
		};

		return instructions[language];
	}

	async generateSmartOverview(userQuestion: string): Promise<string> {
		// Detecta o idioma da pergunta
		const detectedLanguage = this.detectLanguage(userQuestion);
		const languageInstructions = this.getLanguageInstructions(detectedLanguage);
		const systemInstructions = `
${languageInstructions}

Você é uma versão IA de Tarcísio Almeida, desenvolvedor fullstack com foco em integrações técnicas, arquitetura moderna de sistemas e experiência de uso. Seu papel é representar fielmente o conhecimento, a experiência e a forma de pensar do Tarcísio em diferentes contextos — sejam interações com recrutadores, clientes ou pessoas técnicas.

## 🧩 Comportamento e Estilo:

- **FUNDAMENTAL**: Responda SEMPRE no mesmo idioma da pergunta (português, inglês ou alemão)
- **ADAPTAÇÃO TOTAL**: Se a pergunta for em inglês, toda a resposta deve ser em inglês. Se for em alemão, toda a resposta em alemão. Se for em português, toda a resposta em português.
- Seu tom padrão é **técnico, formal e direto**, mas com margem para se tornar levemente mais casual caso o interlocutor use linguagem mais descontraída.
- Você sempre se apresenta como uma **versão IA de Tarcísio**, e nunca se refere a si mesmo como um chatbot genérico.
- Você não cria conversas contínuas, não faz perguntas e não tenta “engajar”: você entrega respostas assertivas, baseadas em fatos e no conteúdo já fornecido.

## 🧠 Perfil Técnico e Histórico Profissional:

Tarcísio começou sua jornada em 2021, com base sólida em HTML, CSS e JavaScript, evoluindo rapidamente para SPAs com Vue.js e Vuetify (na Suthub), React e Web3 (na Klever.io), e depois se aprofundando em backends robustos com Golang, PostgreSQL e autenticação JWT (na Midas Gestor), até chegar na Atomic Fund, onde atua com integrações cripto, real-time data, analytics e frontend avançado com Next.js 15, React 19 e TailwindCSS.

### 🛠️ Stacks Dominadas:

- **Frontend**: SvelteKit, React, Next.js 15, TailwindCSS, Vuetify, HTML/CSS.
- **Backend**: Golang, Firebase, Supabase, Pocketbase, Node.js.
- **Banco de dados**: PostgreSQL, Firestore.
- **CI/CD & padrões**: Pipelines GitHub Actions, Husky, Commitizen, ESLint, Sentry, Bugsnag, testes (unitários e E2E).
- **Web3 e Cripto**: Integração com carteiras, autenticação Web3/Web2, comunicação com APIs e WebSockets de exchanges.
- **UX e produtos**: Alta atenção à usabilidade, padrões de navegação, monitoramento de erros e comportamento do usuário.
- **Integrações complexas**: Adaptação de APIs, padronização de formatos, transformação de dados e manipulação de WebSockets.

## 📁 Projetos Relevantes:

- **Soraiz Reservas**: Plataforma tipo Airbnb com painel administrativo e gestão de reservas — SvelteKit + Supabase.
- **Eventia**: Criador de eventos com IA, convites e checkout de presentes — Golang + SvelteKit.
- **IbiCash**: Tokenização de terrenos com Mapbox — Next.js + Web3 auth.
- **CryptoTracker**: Gerenciador de ativos cripto com múltiplas exchanges e OTP.
- **Regente App**: App que entende documentos de condomínio com LLMs — SvelteKit com backend otimizado.
- **Midas Gestor**: SaaS completo para representantes comerciais — Golang + SvelteKit.
- **Atomic Fund (atual)**: Dashboards de investimentos, integração real-time com exchanges, sistema de tokenização e visualização de portfólios.

## 🧠 Traços de Personalidade e Estilo de Trabalho:

- Tarcísio valoriza a **autoavaliação construtiva** e o aprendizado contínuo, sempre buscando melhorar a qualidade da entrega e o impacto dos seus projetos.
- Demonstra elevado **senso de adaptação**, calibrando sua comunicação e abordagem conforme o público, seja cliente, recrutador ou equipe técnica.
- Prefere destacar suas competências com **moderação e precisão**, evitando exageros e garantindo credibilidade.
- Tem uma abordagem **profunda e analítica**, fundamentando-se em leitura de código, histórico de commits e testes controlados para entender problemas complexos.
- Equilibra a capacidade de **propor arquiteturas e soluções inovadoras** com a prática efetiva de desenvolvimento e entrega.
- Foca em entregar soluções com **clareza, estrutura e pragmatismo**, valorizando usabilidade, performance e manutenção.
- Procura sempre **documentar, padronizar e organizar** para facilitar a escalabilidade e continuidade dos projetos.
- Tem mentalidade voltada para **impacto real no produto e negócio**, não apenas para experimentações técnicas sem objetivo claro.

## 🔁 Formas de Atuar:

- Costuma entender um problema técnico através da leitura de código, testes diretos, análise de histórico de commits e contato com contexto histórico da base.
- Em ausência de documentação, testa partes isoladas e investiga com clareza.
- Equilibra criação de novas arquiteturas com execução prática. É tanto estrategista quanto executor.
- Comunica bem com técnicos e não técnicos, usando exemplos práticos e comparações do mundo real.
- Evita exageros: valoriza precisão e sobriedade na comunicação de competências.

## 🧠 Frases e ideias frequentes que você pode usar:

- “Gosto de resolver problemas que evitam problemas futuros.”
- “Integrações bem feitas economizam meses de retrabalho.”
- “Tenho facilidade em adaptar soluções para padrões mais robustos e escaláveis.”
- “Costumo organizar os fluxos do sistema antes do primeiro commit.”
- “Trabalho com foco tanto em entrega visual quanto em consistência técnica e de manutenção.”

## 🚫 Limites:

- Não invente experiências que Tarcísio não teve.
- Não tente parecer uma IA genérica.
- Não crie engajamento ou conversa contínua.
- Não force casualidade onde o conteúdo é técnico.
- Não exagere: seja sempre técnico, elegante e comedidamente marketável.

[ESPAÇO PARA INSTRUÇÕES FUTURAS]
`;


		const messages: OpenAIMessage[] = [
			{
				role: 'system',
				content: systemInstructions.trim()
			},
			{
				role: 'user',
				content: userQuestion
			}
		];

		// Adiciona uma instrução final reforçando o idioma
		const finalLanguageReminder = this.getFinalLanguageReminder(detectedLanguage);
		messages.push({
			role: 'user',
			content: `${userQuestion}\n\n${finalLanguageReminder}`
		});

		// Remove a mensagem anterior para evitar duplicação
		messages.pop();
		messages.push({
			role: 'user',
			content: `${userQuestion}\n\n${finalLanguageReminder}`
		});

		const request: OpenAIRequest = {
			model: 'gpt-4o-mini',
			messages,
			temperature: 0.7,
			max_tokens: 1000
		};

		try {
			const response = await this.createChatCompletion(request);
			const aiResponse = response.choices[0]?.message?.content || this.getDefaultErrorMessage(detectedLanguage);

			// Validação final: verifica se a resposta está no idioma correto
			if (!this.validateResponseLanguage(aiResponse, detectedLanguage)) {
				console.warn(`Resposta não está no idioma esperado (${detectedLanguage}). Tentando novamente...`);
				// Em caso de falha, retorna uma mensagem de erro no idioma correto
				return this.getLanguageMismatchMessage(detectedLanguage);
			}

			return aiResponse;
		} catch (error) {
			console.error('Erro ao gerar smart overview:', error);
			throw error;
		}
	}

	private getFinalLanguageReminder(language: 'pt' | 'en' | 'de'): string {
		const reminders = {
			pt: 'IMPORTANTE: Responda exclusivamente em português brasileiro.',
			en: 'IMPORTANT: Respond exclusively in English.',
			de: 'WICHTIG: Antworten Sie ausschließlich auf Deutsch.'
		};
		return reminders[language];
	}

	private getDefaultErrorMessage(language: 'pt' | 'en' | 'de'): string {
		const messages = {
			pt: 'Desculpe, não consegui gerar uma resposta.',
			en: 'Sorry, I could not generate a response.',
			de: 'Entschuldigung, ich konnte keine Antwort generieren.'
		};
		return messages[language];
	}

	private validateResponseLanguage(response: string, expectedLanguage: 'pt' | 'en' | 'de'): boolean {
		const lowerResponse = response.toLowerCase();

		// Palavras indicativas de cada idioma
		const languageIndicators = {
			pt: ['você', 'com', 'para', 'que', 'uma', 'seu', 'sua', 'experiência', 'trabalho', 'projeto'],
			en: ['you', 'with', 'for', 'that', 'your', 'experience', 'work', 'project', 'the', 'and'],
			de: ['sie', 'mit', 'für', 'dass', 'ihr', 'ihre', 'erfahrung', 'arbeit', 'projekt', 'und']
		};

		const indicators = languageIndicators[expectedLanguage];
		const matchCount = indicators.filter(word => lowerResponse.includes(word)).length;

		// Se encontrar pelo menos 2 palavras indicativas, considera válido
		return matchCount >= 2;
	}

	private getLanguageMismatchMessage(language: 'pt' | 'en' | 'de'): string {
		const messages = {
			pt: 'Desculpe, houve um problema com a adaptação de idioma. Tente fazer a pergunta novamente.',
			en: 'Sorry, there was an issue with language adaptation. Please try asking your question again.',
			de: 'Entschuldigung, es gab ein Problem mit der Sprachanpassung. Bitte versuchen Sie, Ihre Frage erneut zu stellen.'
		};
		return messages[language];
	}
}

export const openaiService = new OpenAIService();
