import type { LangContentStructure } from ".";

export const GERMAN_CONTENT: LangContentStructure = {
    homePage: {
        jobTitle: "Software Engineer",
        buttons: {
            about: "Über mich",
            contact: "Konta<PERSON>"
        },
        langMenu: {
            title: "<PERSON>prache",
            options: {
                pt: "Portugiesisch",
                en: "Englisch",
                ger: "Deutsch"
            }
        },
        soundMenu: {
            title: "Sound",
            options: {
                on: "An",
                off: "Aus"
            }
        },
        tabMenu: {
            title: "Menü",
            options: {
                smartOverview: "smart overview",
                experience: "erfahrung",
                projects: "projekte"
            }
        }
    },
    experienceRoadmap: {
        imageAlt: "Tarcísio AI-Assistent",
        description: "Meine berufliche Reise in der Technologie, von den ersten Schritten bis zu aktuellen Erfolgen.",
        subtitle: "Eine Roadmap meiner Entwicklung in der Tech-Karriere.",
        loadingText: "Roadmap wird geladen...",
        roadmapDefinition: `
    flowchart TD
      %% Beginn der Reise - 2021
      Start([🎯 2021 - Studienbeginn]) --> Basics[📖 Grundstudien]
      Basics --> HTML[🌐 HTML5 & Semantik]
      Basics --> CSS[🎨 CSS3 & Flexbox/Grid]
      Basics --> JS[⚡ JavaScript ES6+]

      HTML --> WebFund[🔧 Web-Grundlagen]
      CSS --> WebFund
      JS --> WebFund
      WebFund --> FirstProjects[💻 Erste Projekte]
      FirstProjects --> Freelance[💼 Freelance für Freunde]

      %% Karriereübergang - 2022
      Freelance --> Transition[🚀 Karriereübergang]
      Transition --> SUTHUB([🏢 2022 - SUTHUB<br/>Erster Job])

      %% SUTHUB - Entwicklung
      SUTHUB --> VueEco[🌿 Vue.js Ökosystem]
      VueEco --> Vue[⚡ Vue.js & Composition API]
      VueEco --> Vuetify[🎯 Vuetify Material Design]
      VueEco --> VueRouter[🛣️ Vue Router & State]

      Vue --> SPAs[📱 Single Page Applications]
      Vuetify --> LandingPages[🎨 Optimierte Landing Pages]
      VueRouter --> InsurancePlatforms[🛡️ Versicherungsplattformen]

      SPAs --> SuthubProjects[📋 SUTHUB Projekte]
      LandingPages --> SuthubProjects
      InsurancePlatforms --> SuthubProjects

      SuthubProjects --> MAPFRE[🏛️ MAPFRE - Allgemeine Versicherung]
      SuthubProjects --> APet[🐕 APet - Haustierversicherung]
      SuthubProjects --> SagaBike[🚴 SagaBike - Fahrradversicherung]

      MAPFRE --> CheckoutFlow[💳 Benutzerdefinierter Checkout]
      APet --> CheckoutFlow
      SagaBike --> CheckoutFlow

      CheckoutFlow --> PythonIntro[🐍 Python Einführung]
      PythonIntro --> PDFGeneration[📄 Dynamische PDF-Generierung]
      PDFGeneration --> LegacyCode[🔧 Legacy-Code Wartung]

      %% Übergang zu Klever
      LegacyCode --> KleverTransition[🔄 Evolution zu Web3]
      KleverTransition --> KLEVER([🔗 Klever.io<br/>Blockchain & Crypto])

      %% KLEVER - Web3 und Blockchain
      KLEVER --> ReactEco[⚛️ React Ökosystem]
      ReactEco --> ReactJS[⚛️ React.js & Hooks]
      ReactEco --> ReactQuery[🔄 React Query & Cache]
      ReactEco --> ReactTesting[🧪 Testing Library]

      ReactJS --> Web3Integration[🌐 Web3 Integration]
      Web3Integration --> WalletConnect[💰 Wallet-Verbindung]
      Web3Integration --> BlockchainTx[⛓️ Blockchain-Transaktionen]

      ReactQuery --> Performance[⚡ Performance-Optimierung]
      Performance --> KleverProjects[📋 Klever Projekte]

      KleverProjects --> KleverSwap[🔄 Klever Swap DEX]
      KleverProjects --> KleverExchange[📈 Klever Exchange]

      KleverSwap --> SwapFeatures[💱 Swap-Funktionen]
      KleverExchange --> ExchangeFeatures[📊 Exchange-Funktionen]

      SwapFeatures --> TestingPractices[🧪 Test-Praktiken]
      ExchangeFeatures --> TestingPractices

      TestingPractices --> UnitTests[🔬 Unit-Tests]
      TestingPractices --> E2ETests[🎭 End-to-End Tests]
      TestingPractices --> CICD1[🚀 CI/CD Pipelines]

      UnitTests --> ErrorMonitoring[🐛 Fehlerüberwachung - Bugsnag]
      E2ETests --> ErrorMonitoring
      CICD1 --> ErrorMonitoring

      ErrorMonitoring --> PerformanceOpt[⚡ Performance & Speicher-Optimierung]

      %% Übergang zu Midas Gestor
      PerformanceOpt --> MidasTransition[🔄 Evolution zu SaaS]
      MidasTransition --> MIDAS([💼 Midas Gestor<br/>Vertriebsvertreter-Management])

      %% MIDAS - SaaS und Backend
      MIDAS --> GoLang[🐹 GoLang Backend]
      GoLang --> APIDesign[🔌 RESTful API Design]
      GoLang --> DatabaseDesign[🗄️ PostgreSQL Datenbank Design]
      GoLang --> Authentication[🔐 JWT Authentifizierung]

      APIDesign --> SvelteKit[🚀 SvelteKit Frontend]
      DatabaseDesign --> SvelteKit
      Authentication --> SvelteKit

      SvelteKit --> FullStackDev[🌐 Full-Stack Entwicklung]
      FullStackDev --> MidasFeatures[📋 Midas Features]

      MidasFeatures --> CatalogManagement[📦 Produktkatalog-Management]
      MidasFeatures --> PDFReports[📄 PDF-Bericht-Generierung]
      MidasFeatures --> DataImport[📊 Datenimport-Systeme]
      MidasFeatures --> ClientManagement[👥 Kunden-Management]

      CatalogManagement --> AtomicTransition[🔄 Evolution zu Investitionen]
      PDFReports --> AtomicTransition
      DataImport --> AtomicTransition
      ClientManagement --> AtomicTransition

      %% Atomic Fund - Aktuell
      AtomicTransition --> ATOMIC([💰 Atomic Fund<br/>Investment-Plattform])

      ATOMIC --> NextJS[⚛️ Next.js 15 & React 19]
      NextJS --> AdvancedReact[🧠 Erweiterte React-Muster]
      NextJS --> TailwindAdvanced[🎨 Erweiterte Tailwind CSS]
      NextJS --> TypeScriptAdvanced[📘 Erweiterte TypeScript]

      AdvancedReact --> ModernFeatures[🚀 Moderne Features]
      TailwindAdvanced --> ModernFeatures
      TypeScriptAdvanced --> ModernFeatures

      ModernFeatures --> InvestmentDashboard[📈 Investment-Dashboard]
      ModernFeatures --> UserPortfolio[💼 Benutzer-Portfolio-Management]
      ModernFeatures --> RealTimeData[📊 Echtzeit-Datenintegration]

      InvestmentDashboard --> FutureGoals[🎯 Zukunftsziele]
      UserPortfolio --> FutureGoals
      RealTimeData --> FutureGoals

      FutureGoals --> Leadership[👑 Technische Führung]
      FutureGoals --> Innovation[💡 Innovation & F&E]
      FutureGoals --> Mentorship[🎓 Mentoring & Lehre]

      %% Styling
      classDef startEndStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px
      classDef foundationsStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
      classDef suthubStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
      classDef kleverStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
      classDef midasStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
      classDef atomicStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px

      class Start,FutureGoals,Leadership,Innovation,Mentorship startEndStyle
      class Basics,HTML,CSS,JS,WebFund,FirstProjects,Freelance foundationsStyle
      class SUTHUB,VueEco,Vue,Vuetify,VueRouter,SPAs,LandingPages,InsurancePlatforms,SuthubProjects,MAPFRE,APet,SagaBike,CheckoutFlow,PythonIntro,PDFGeneration,LegacyCode suthubStyle
      class KLEVER,ReactEco,ReactJS,ReactQuery,ReactTesting,Web3Integration,WalletConnect,BlockchainTx,Performance,KleverProjects,KleverSwap,KleverExchange,SwapFeatures,ExchangeFeatures,TestingPractices,UnitTests,E2ETests,CICD1,ErrorMonitoring,PerformanceOpt kleverStyle
      class MIDAS,GoLang,APIDesign,DatabaseDesign,Authentication,SvelteKit,FullStackDev,MidasFeatures,CatalogManagement,PDFReports,DataImport,ClientManagement midasStyle
      class ATOMIC,NextJS,AdvancedReact,TailwindAdvanced,TypeScriptAdvanced,ModernFeatures,InvestmentDashboard,UserPortfolio,RealTimeData atomicStyle
    `,
        legend: {
            startEnd: "Start/Zukunft",
            foundations: "Grundlagen (2021)",
            suthub: "SUTHUB (2022)",
            klever: "Klever.io",
            midas: "Midas Gestor",
            atomic: "Atomic Fund (Aktuell)"
        }
    },
    projectsList: {
        imageAlt: "Tarcísio Projekte",
        description: "Eine Auswahl meiner relevantesten Projekte, die Vielseitigkeit und technische Expertise demonstrieren.",
        viewProject: "Projekt ansehen",
        projects: {
            soraizReservas: {
                title: "Soraiz Reservas",
                description: "Vollständige Reservierungsplattform inspiriert von Airbnb, mit Verwaltungspanel für Reservierungsmanagement und internem Content-Management-System. Vollständig mit SvelteKit und Supabase entwickelt."
            },
            eventia: {
                title: "Eventia",
                description: "Innovative Plattform zur Erstellung von Events mit künstlicher Intelligenz. Bietet schnelle Event-Erstellung, Einladungsversandsystem und Geschenkkatalog mit integriertem Checkout. Backend in Golang und Frontend in SvelteKit."
            },
            ibiCash: {
                title: "IbiCash",
                description: "Landtokenisierungsplattform mit interaktiver Kartenintegration über Mapbox. Frontend in Next.js entwickelt mit aktiver Beteiligung an der App-Entwicklung und Erstellung der interaktiven Landing Page."
            },
            midasGestor: {
                title: "Midas Gestor",
                description: "Managementplattform spezialisiert für Handelsvertreter. SvelteKit-Frontend integriert mit Golang-Backend, einschließlich PDF-Berichtgenerierung, Datenimport und vollständiger Katalogverwaltung."
            },
            cryptoTracker: {
                title: "CryptoTracker",
                description: "Erweiterte Plattform für die Verwaltung kryptografischer Assets von mehreren Börsen. Funktionen umfassen Saldovisualisierung, Leistungsvergleich zwischen Börsen und sichere OTP-Authentifizierung."
            },
            regenteApp: {
                title: "Regente App",
                description: "Innovatives App-Konzept mit integriertem LLM-System für intelligentes Verständnis von Eigentümergemeinschaftsordnungen und anderen Dokumenten. Backend optimiert für Token-Ökonomie, vollständig von SvelteKit verwaltet."
            }
        }
    },
    smartOverview: {
        imageAlt: "Tarcísio AI-Assistent",
        description: "Nutzen Sie diesen KI-integrierten Chat, um Fragen zu meiner beruflichen Erfahrung zu stellen. Erhalten Sie vereinfachte und direkte Antworten über meine Karriere, Projekte und Fähigkeiten.",
        questionLabel: "Stellen Sie Ihre Frage:",
        questionPlaceholder: "Z.B.: Was ist Tarcísios Erfahrung in React?",
        examplesTitle: "Beispielfragen:",
        responseText: "Antworten erscheinen hier...",
        exampleQuestions: {
            experience: "Was ist Tarcísios Erfahrung in der Entwicklung?",
            technologies: "Welche Technologien beherrscht er?",
            projects: "Erzählen Sie von seinen relevantesten Projekten"
        }
    }
}