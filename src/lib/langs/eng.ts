import { type LangContentStructure } from "."

export const ENGLISH_CONTENT: LangContentStructure = {
    homePage: {
        jobTitle: "Software Engineer",
        buttons: {
            about: "about",
            contact: "contact"
        },
        langMenu: {
            title: "Language",
            options: {
                pt: "Portuguese",
                en: "English",
                ger: "German"
            }
        },
        soundMenu: {
            title: "Sound",
            options: {
                on: "On",
                off: "Off"
            }
        },
        tabMenu: {
            title: "Menu",
            options: {
                smartOverview: "smart overview",
                experience: "experience",
                projects: "projects"
            }
        }
    },
    experienceRoadmap: {
        imageAlt: "Tarcísio AI Assistant",
        description: "My professional journey in technology, from the first steps to current achievements.",
        subtitle: "A roadmap of my evolution in the tech career.",
        loadingText: "Loading roadmap...",
        roadmapDefinition: `
    flowchart TD
      %% Journey Beginning - 2021
      Start([🎯 2021 - Study Beginning]) --> Basics[📖 Basic Studies]
      Basics --> HTML[🌐 HTML5 & Semantics]
      Basics --> CSS[🎨 CSS3 & Flexbox/Grid]
      Basics --> JS[⚡ JavaScript ES6+]

      HTML --> WebFund[🔧 Web Fundamentals]
      CSS --> WebFund
      JS --> WebFund
      WebFund --> FirstProjects[💻 First Projects]
      FirstProjects --> Freelance[💼 Freelances for Friends]

      %% Career Transition - 2022
      Freelance --> Transition[🚀 Career Transition]
      Transition --> SUTHUB([🏢 2022 - SUTHUB<br/>First Job])

      %% SUTHUB - Development
      SUTHUB --> VueEco[🌿 Vue.js Ecosystem]
      VueEco --> Vue[⚡ Vue.js & Composition API]
      VueEco --> Vuetify[🎯 Vuetify Material Design]
      VueEco --> VueRouter[🛣️ Vue Router & State]

      Vue --> SPAs[📱 Single Page Applications]
      Vuetify --> LandingPages[🎨 Optimized Landing Pages]
      VueRouter --> InsurancePlatforms[🛡️ Insurance Platforms]

      SPAs --> SuthubProjects[📋 SUTHUB Projects]
      LandingPages --> SuthubProjects
      InsurancePlatforms --> SuthubProjects

      SuthubProjects --> MAPFRE[🏛️ MAPFRE - General Insurance]
      SuthubProjects --> APet[🐕 APet - Pet Insurance]
      SuthubProjects --> SagaBike[🚴 SagaBike - Bike Insurance]

      MAPFRE --> CheckoutFlow[💳 Custom Checkout]
      APet --> CheckoutFlow
      SagaBike --> CheckoutFlow

      CheckoutFlow --> PythonIntro[🐍 Python Introduction]
      PythonIntro --> PDFGeneration[📄 Dynamic PDF Generation]
      PDFGeneration --> LegacyCode[🔧 Legacy Code Maintenance]

      %% Transition to Klever
      LegacyCode --> KleverTransition[🔄 Evolution to Web3]
      KleverTransition --> KLEVER([🔗 Klever.io<br/>Blockchain & Crypto])

      %% KLEVER - Web3 and Blockchain
      KLEVER --> ReactEco[⚛️ React Ecosystem]
      ReactEco --> ReactJS[⚛️ React.js & Hooks]
      ReactEco --> ReactQuery[🔄 React Query & Cache]
      ReactEco --> ReactTesting[🧪 Testing Library]

      ReactJS --> Web3Integration[🌐 Web3 Integration]
      Web3Integration --> WalletConnect[💰 Wallet Connection]
      Web3Integration --> BlockchainTx[⛓️ Blockchain Transactions]

      ReactQuery --> Performance[⚡ Performance Optimization]
      Performance --> KleverProjects[📋 Klever Projects]

      KleverProjects --> KleverSwap[🔄 Klever Swap DEX]
      KleverProjects --> KleverExchange[📈 Klever Exchange]

      KleverSwap --> SwapFeatures[💱 Swap Features]
      KleverExchange --> ExchangeFeatures[📊 Exchange Features]

      SwapFeatures --> TestingPractices[🧪 Testing Practices]
      ExchangeFeatures --> TestingPractices

      TestingPractices --> UnitTests[🔬 Unit Tests]
      TestingPractices --> E2ETests[🎭 End-to-End Tests]
      TestingPractices --> CICD1[🚀 CI/CD Pipelines]

      UnitTests --> ErrorMonitoring[🐛 Error Monitoring - Bugsnag]
      E2ETests --> ErrorMonitoring
      CICD1 --> ErrorMonitoring

      ErrorMonitoring --> PerformanceOpt[⚡ Performance & Memory Optimization]

      %% Transition to Midas Gestor
      PerformanceOpt --> MidasTransition[🔄 Evolution to SaaS]
      MidasTransition --> MIDAS([💼 Midas Gestor<br/>Sales Rep Management])

      %% MIDAS - SaaS and Backend
      MIDAS --> GoLang[🐹 GoLang Backend]
      GoLang --> APIDesign[🔌 RESTful API Design]
      GoLang --> DatabaseDesign[🗄️ PostgreSQL Database Design]
      GoLang --> Authentication[🔐 JWT Authentication]

      APIDesign --> SvelteKit[🚀 SvelteKit Frontend]
      DatabaseDesign --> SvelteKit
      Authentication --> SvelteKit

      SvelteKit --> FullStackDev[🌐 Full-Stack Development]
      FullStackDev --> MidasFeatures[📋 Midas Features]

      MidasFeatures --> CatalogManagement[📦 Product Catalog Management]
      MidasFeatures --> PDFReports[📄 PDF Report Generation]
      MidasFeatures --> DataImport[📊 Data Import Systems]
      MidasFeatures --> ClientManagement[👥 Client Management]

      CatalogManagement --> AtomicTransition[🔄 Evolution to Investments]
      PDFReports --> AtomicTransition
      DataImport --> AtomicTransition
      ClientManagement --> AtomicTransition

      %% Atomic Fund - Current
      AtomicTransition --> ATOMIC([💰 Atomic Fund<br/>Investment Platform])

      ATOMIC --> NextJS[⚛️ Next.js 15 & React 19]
      NextJS --> AdvancedReact[🧠 Advanced React Patterns]
      NextJS --> TailwindAdvanced[🎨 Advanced Tailwind CSS]
      NextJS --> TypeScriptAdvanced[📘 Advanced TypeScript]

      AdvancedReact --> ModernFeatures[🚀 Modern Features]
      TailwindAdvanced --> ModernFeatures
      TypeScriptAdvanced --> ModernFeatures

      ModernFeatures --> InvestmentDashboard[📈 Investment Dashboard]
      ModernFeatures --> UserPortfolio[💼 User Portfolio Management]
      ModernFeatures --> RealTimeData[📊 Real-time Data Integration]

      InvestmentDashboard --> FutureGoals[🎯 Future Goals]
      UserPortfolio --> FutureGoals
      RealTimeData --> FutureGoals

      FutureGoals --> Leadership[👑 Technical Leadership]
      FutureGoals --> Innovation[💡 Innovation & R&D]
      FutureGoals --> Mentorship[🎓 Mentoring & Teaching]

      %% Styling
      classDef startEndStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px
      classDef foundationsStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
      classDef suthubStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
      classDef kleverStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
      classDef midasStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
      classDef atomicStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px

      class Start,FutureGoals,Leadership,Innovation,Mentorship startEndStyle
      class Basics,HTML,CSS,JS,WebFund,FirstProjects,Freelance foundationsStyle
      class SUTHUB,VueEco,Vue,Vuetify,VueRouter,SPAs,LandingPages,InsurancePlatforms,SuthubProjects,MAPFRE,APet,SagaBike,CheckoutFlow,PythonIntro,PDFGeneration,LegacyCode suthubStyle
      class KLEVER,ReactEco,ReactJS,ReactQuery,ReactTesting,Web3Integration,WalletConnect,BlockchainTx,Performance,KleverProjects,KleverSwap,KleverExchange,SwapFeatures,ExchangeFeatures,TestingPractices,UnitTests,E2ETests,CICD1,ErrorMonitoring,PerformanceOpt kleverStyle
      class MIDAS,GoLang,APIDesign,DatabaseDesign,Authentication,SvelteKit,FullStackDev,MidasFeatures,CatalogManagement,PDFReports,DataImport,ClientManagement midasStyle
      class ATOMIC,NextJS,AdvancedReact,TailwindAdvanced,TypeScriptAdvanced,ModernFeatures,InvestmentDashboard,UserPortfolio,RealTimeData atomicStyle
    `,
        legend: {
            startEnd: "Start/Future",
            foundations: "Foundations (2021)",
            suthub: "SUTHUB (2022)",
            klever: "Klever.io",
            midas: "Midas Gestor",
            atomic: "Atomic Fund (Current)"
        }
    },
    projectsList: {
        imageAlt: "Tarcísio Projects",
        description: "A selection of my most relevant projects, demonstrating versatility and technical expertise.",
        viewProject: "View project",
        projects: {
            soraizReservas: {
                title: "Soraiz Reservas",
                description: "Complete reservation platform inspired by Airbnb, with administrative panel for reservation management and internal content management system. Developed entirely with SvelteKit and Supabase."
            },
            eventia: {
                title: "Eventia",
                description: "Innovative platform for creating events with artificial intelligence. Offers quick event creation, invitation sending system and gift catalog with integrated checkout. Backend in Golang and frontend in SvelteKit."
            },
            ibiCash: {
                title: "IbiCash",
                description: "Land tokenization platform with interactive map integration via Mapbox. Frontend developed in Next.js with active participation in app development and creation of interactive landing page."
            },
            midasGestor: {
                title: "Midas Gestor",
                description: "Management platform specialized for commercial representatives. SvelteKit frontend integrated with Golang backend, including PDF report generation, data import and complete catalog management."
            },
            cryptoTracker: {
                title: "CryptoTracker",
                description: "Advanced platform for managing cryptographic assets from multiple exchanges. Features include balance visualization, performance comparison between exchanges and secure OTP authentication."
            },
            regenteApp: {
                title: "Regente App",
                description: "Innovative app concept with integrated LLM system for intelligent understanding of condominium bylaws and other documents. Backend optimized for token economy, fully managed by SvelteKit."
            }
        }
    },
    smartOverview: {
        imageAlt: "Tarcísio AI Assistant",
        description: "Use this AI-integrated chat to ask questions about my professional experience. Get simplified and direct answers about my career, projects and skills.",
        questionLabel: "Ask your question:",
        questionPlaceholder: "Ex: What is Tarcísio's experience in React?",
        examplesTitle: "Example questions:",
        responseText: "Answers will appear here...",
        exampleQuestions: {
            experience: "What is Tarcísio's experience in development?",
            technologies: "What technologies does he master?",
            projects: "Tell me about his most relevant projects"
        }
    }
}