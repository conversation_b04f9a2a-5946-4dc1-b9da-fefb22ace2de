import type { LangContentStructure } from ".";

export const PORTUGUESE_CONTENT: LangContentStructure = {
    homePage: {
        jobTitle: "Engenheiro de Software",
        buttons: {
            about: "sobre",
            contact: "contato"
        },
        langMenu: {
            title: "Idioma",
            options: {
                pt: "Português",
                en: "Inglês",
                ger: "Alemão"
            }
        },
        soundMenu: {
            title: "Som",
            options: {
                on: "Ligar",
                off: "Desligar"
            }
        },
        tabMenu: {
            title: "Menu",
            options: {
                smartOverview: "visão geral inteligente",
                experience: "experiência",
                projects: "projetos"
            }
        }
    },
    experienceRoadmap: {
        imageAlt: "Tarcísio AI Assistant",
        description: "Minha jornada profissional em tecnologia, desde os primeiros passos até conquistas atuais.",
        subtitle: "Um roadmap da minha evolução na carreira tech.",
        loadingText: "Carregando roadmap...",
        roadmapDefinition: `
    flowchart TD
      %% Início da Jornada - 2021
      Start([🎯 2021 - <PERSON><PERSON><PERSON> dos Estudos]) --> Basics[📖 Estudos Básicos]
      Basics --> HTML[🌐 HTML5 & Semântica]
      Basics --> CSS[🎨 CSS3 & Flexbox/Grid]
      Basics --> JS[⚡ JavaScript ES6+]

      HTML --> WebFund[🔧 Fundamentos Web]
      CSS --> WebFund
      JS --> WebFund
      WebFund --> FirstProjects[💻 Primeiros Projetos]
      FirstProjects --> Freelance[💼 Freelances para Amigos]

      %% Transição para Carreira - 2022
      Freelance --> Transition[🚀 Transição para Carreira]
      Transition --> SUTHUB([🏢 2022 - SUTHUB<br/>Primeiro Emprego])

      %% SUTHUB - Desenvolvimento
      SUTHUB --> VueEco[🌿 Ecossistema Vue.js]
      VueEco --> Vue[⚡ Vue.js & Composition API]
      VueEco --> Vuetify[🎯 Vuetify Material Design]
      VueEco --> VueRouter[🛣️ Vue Router & State]

      Vue --> SPAs[📱 Single Page Applications]
      Vuetify --> LandingPages[🎨 Landing Pages Otimizadas]
      VueRouter --> InsurancePlatforms[🛡️ Plataformas de Seguros]

      SPAs --> SuthubProjects[📋 Projetos SUTHUB]
      LandingPages --> SuthubProjects
      InsurancePlatforms --> SuthubProjects

      SuthubProjects --> MAPFRE[🏛️ MAPFRE - Seguros Gerais]
      SuthubProjects --> APet[🐕 APet - Seguros Pet]
      SuthubProjects --> SagaBike[🚴 SagaBike - Seguros Bike]

      MAPFRE --> CheckoutFlow[💳 Checkout Customizado]
      APet --> CheckoutFlow
      SagaBike --> CheckoutFlow

      CheckoutFlow --> PythonIntro[🐍 Introdução ao Python]
      PythonIntro --> PDFGeneration[📄 Geração Dinâmica de PDFs]
      PDFGeneration --> LegacyCode[🔧 Manutenção Código Legado]

      %% Transição para Klever
      LegacyCode --> KleverTransition[🔄 Evolução para Web3]
      KleverTransition --> KLEVER([🔗 Klever.io<br/>Blockchain & Crypto])

      %% KLEVER - Web3 e Blockchain
      KLEVER --> ReactEco[⚛️ Ecossistema React]
      ReactEco --> ReactJS[⚛️ React.js & Hooks]
      ReactEco --> ReactQuery[🔄 React Query & Cache]
      ReactEco --> ReactTesting[🧪 Testing Library]

      ReactJS --> Web3Integration[🌐 Integração Web3]
      Web3Integration --> WalletConnect[💰 Conexão com Carteiras]
      Web3Integration --> BlockchainTx[⛓️ Transações Blockchain]

      ReactQuery --> Performance[⚡ Otimização Performance]
      Performance --> KleverProjects[📋 Projetos Klever]

      KleverProjects --> KleverSwap[🔄 Klever Swap DEX]
      KleverProjects --> KleverExchange[📈 Klever Exchange]

      KleverSwap --> SwapFeatures[💱 Funcionalidades Swap]
      KleverExchange --> ExchangeFeatures[📊 Funcionalidades Exchange]

      SwapFeatures --> TestingPractices[🧪 Práticas de Testes]
      ExchangeFeatures --> TestingPractices

      TestingPractices --> UnitTests[🔬 Testes Unitários]
      TestingPractices --> E2ETests[🎭 Testes End-to-End]
      TestingPractices --> CICD1[🚀 CI/CD Pipelines]

      UnitTests --> ErrorMonitoring[🐛 Monitoramento - Bugsnag]
      E2ETests --> ErrorMonitoring
      CICD1 --> ErrorMonitoring

      ErrorMonitoring --> PerformanceOpt[⚡ Otimização Performance & Memória]

      %% Transição para Midas Gestor
      PerformanceOpt --> MidasTransition[🔄 Evolução para SaaS]
      MidasTransition --> MIDAS([💼 Midas Gestor<br/>Gestão Rep. Comerciais])

      %% MIDAS - SaaS e Backend
      MIDAS --> GoLang[🐹 GoLang Backend]
      GoLang --> APIDesign[🔌 Design APIs RESTful]
      GoLang --> DatabaseDesign[🗄️ Design Database PostgreSQL]
      GoLang --> Authentication[🔐 Autenticação JWT]

      APIDesign --> SvelteKit[🚀 SvelteKit Frontend]
      DatabaseDesign --> SvelteKit
      Authentication --> SvelteKit

      SvelteKit --> FullStackDev[🌐 Desenvolvimento Full-Stack]
      FullStackDev --> MidasFeatures[📋 Features Midas]

      MidasFeatures --> CatalogManagement[📦 Gestão Catálogo Produtos]
      MidasFeatures --> PDFReports[📄 Geração Relatórios PDF]
      MidasFeatures --> DataImport[📊 Sistemas Importação Dados]
      MidasFeatures --> ClientManagement[👥 Gestão de Clientes]

      CatalogManagement --> AtomicTransition[🔄 Evolução para Investimentos]
      PDFReports --> AtomicTransition
      DataImport --> AtomicTransition
      ClientManagement --> AtomicTransition

      %% Atomic Fund - Atual
      AtomicTransition --> ATOMIC([💰 Atomic Fund<br/>Plataforma Investimentos])

      ATOMIC --> NextJS[⚛️ Next.js 15 & React 19]
      NextJS --> AdvancedReact[🧠 Padrões Avançados React]
      NextJS --> TailwindAdvanced[🎨 Tailwind CSS Avançado]
      NextJS --> TypeScriptAdvanced[📘 TypeScript Avançado]

      AdvancedReact --> ModernFeatures[🚀 Features Modernas]
      TailwindAdvanced --> ModernFeatures
      TypeScriptAdvanced --> ModernFeatures

      ModernFeatures --> InvestmentDashboard[📈 Dashboard Investimentos]
      ModernFeatures --> UserPortfolio[💼 Gestão Portfolio Usuário]
      ModernFeatures --> RealTimeData[📊 Integração Dados Real-time]

      InvestmentDashboard --> FutureGoals[🎯 Objetivos Futuros]
      UserPortfolio --> FutureGoals
      RealTimeData --> FutureGoals

      FutureGoals --> Leadership[👑 Liderança Técnica]
      FutureGoals --> Innovation[💡 Inovação & P&D]
      FutureGoals --> Mentorship[🎓 Mentoria & Ensino]

      %% Estilização
      classDef startEndStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px
      classDef foundationsStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
      classDef suthubStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
      classDef kleverStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
      classDef midasStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
      classDef atomicStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px

      class Start,FutureGoals,Leadership,Innovation,Mentorship startEndStyle
      class Basics,HTML,CSS,JS,WebFund,FirstProjects,Freelance foundationsStyle
      class SUTHUB,VueEco,Vue,Vuetify,VueRouter,SPAs,LandingPages,InsurancePlatforms,SuthubProjects,MAPFRE,APet,SagaBike,CheckoutFlow,PythonIntro,PDFGeneration,LegacyCode suthubStyle
      class KLEVER,ReactEco,ReactJS,ReactQuery,ReactTesting,Web3Integration,WalletConnect,BlockchainTx,Performance,KleverProjects,KleverSwap,KleverExchange,SwapFeatures,ExchangeFeatures,TestingPractices,UnitTests,E2ETests,CICD1,ErrorMonitoring,PerformanceOpt kleverStyle
      class MIDAS,GoLang,APIDesign,DatabaseDesign,Authentication,SvelteKit,FullStackDev,MidasFeatures,CatalogManagement,PDFReports,DataImport,ClientManagement midasStyle
      class ATOMIC,NextJS,AdvancedReact,TailwindAdvanced,TypeScriptAdvanced,ModernFeatures,InvestmentDashboard,UserPortfolio,RealTimeData atomicStyle
    `,
        legend: {
            startEnd: "Início/Futuro",
            foundations: "Fundamentos (2021)",
            suthub: "SUTHUB (2022)",
            klever: "Klever.io",
            midas: "Midas Gestor",
            atomic: "Atomic Fund (Atual)"
        }
    },
    projectsList: {
        imageAlt: "Tarcísio Projects",
        description: "Uma seleção dos meus projetos mais relevantes, demonstrando versatilidade e expertise técnica.",
        viewProject: "Ver projeto",
        projects: {
            soraizReservas: {
                title: "Soraiz Reservas",
                description: "Plataforma completa de reservas inspirada no Airbnb, com painel administrativo para gestão de reservas e sistema interno de gerenciamento de conteúdo. Desenvolvida inteiramente com SvelteKit e Supabase."
            },
            eventia: {
                title: "Eventia",
                description: "Plataforma inovadora para criação de eventos com inteligência artificial. Oferece criação rápida de eventos, sistema de envio de convites e catálogo de presentes com checkout integrado. Backend em Golang e frontend em SvelteKit."
            },
            ibiCash: {
                title: "IbiCash",
                description: "Plataforma de tokenização de terrenos com integração de mapas interativos via Mapbox. Frontend desenvolvido em Next.js com participação ativa na construção do aplicativo e criação da landing page interativa."
            },
            midasGestor: {
                title: "Midas Gestor",
                description: "Plataforma de gerenciamento especializada para representantes comerciais. Frontend em SvelteKit integrado com backend em Golang, incluindo geração de relatórios em PDF, importação de dados e gestão completa de catálogo."
            },
            cryptoTracker: {
                title: "CryptoTracker",
                description: "Plataforma avançada de gerenciamento de ativos criptográficos de múltiplas exchanges. Recursos incluem visualização de saldos, comparação de performance entre exchanges e autenticação segura com OTP."
            },
            regenteApp: {
                title: "Regente App",
                description: "Conceito inovador de aplicativo com sistema LLM integrado para compreensão inteligente de regimentos condominiais e outros documentos. Backend otimizado para economia de tokens, totalmente gerenciado pelo SvelteKit."
            }
        }
    },
    smartOverview: {
        imageAlt: "Tarcísio AI Assistant",
        description: "Use este chat integrado com IA para fazer perguntas sobre minha experiência profissional. Receba respostas simplificadas e diretas sobre minha carreira, projetos e habilidades.",
        questionLabel: "Faça sua pergunta:",
        questionPlaceholder: "Ex: Qual é a experiência do Tarcísio em React?",
        examplesTitle: "Exemplos de perguntas:",
        responseText: "As respostas aparecerão aqui...",
        exampleQuestions: {
            experience: "Qual é a experiência do Tarcísio em desenvolvimento?",
            technologies: "Quais tecnologias ele domina?",
            projects: "Conte sobre os projetos mais relevantes dele"
        }
    }
}