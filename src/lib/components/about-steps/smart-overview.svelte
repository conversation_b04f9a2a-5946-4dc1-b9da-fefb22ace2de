<script lang="ts">
	import Icon from '$lib/components/ui/icon.svelte';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { fade } from 'svelte/transition';
	import { globals } from '$lib/states/global.state.svelte';

	let userQuestion = $state('');
	let isLoading = $state(false);
	let response = $state('');
	let error = $state('');

	function handleExampleClick(question: string) {
		userQuestion = question;
	}

	async function handleSubmit() {
		if (!userQuestion.trim() || isLoading) return;

		isLoading = true;
		error = '';
		response = '';

		try {
			const res = await fetch('/api/openai', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ question: userQuestion.trim() })
			});

			const data = await res.json();

			if (!res.ok) {
				throw new Error(data.error || 'Erro ao processar a solicitação');
			}

			response = data.response;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Erro desconhecido';
			console.error('Erro ao enviar pergunta:', err);
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="mt-6 space-y-6" in:fade>
	<div>
		<div class="flex justify-center">
			<div class="relative">
				<div
					class="size-32 overflow-hidden rounded-full border-2 border-white/20 bg-white/10 shadow-xl backdrop-blur-md"
				>
					<img
						src="https://ik.imagekit.io/og7loqgh2/holdin-ai.png?updatedAt=1751391136997"
						alt={globals.texts().smartOverview.imageAlt}
						class="h-full w-full object-cover"
					/>
				</div>
			</div>
		</div>

		<div class="mt-3 text-center">
			<p class="mx-auto max-w-md text-sm leading-relaxed text-gray-600">
				{globals.texts().smartOverview.description}
			</p>
		</div>
	</div>

	<div class="space-y-2">
		<Label for="question-input" class="text-sm font-semibold text-gray-700">
			{globals.texts().smartOverview.questionLabel}
		</Label>
		<div class="flex gap-2">
			<Input
				id="question-input"
				bind:value={userQuestion}
				placeholder={globals.texts().smartOverview.questionPlaceholder}
				class="flex-1 border-white/20 bg-white/20 backdrop-blur-md placeholder:text-gray-500"
				onkeydown={(e) => e.key === 'Enter' && handleSubmit()}
			/>
			<button
				onclick={handleSubmit}
				disabled={!userQuestion.trim() || isLoading}
				class="font-heading relative flex w-fit cursor-pointer items-center overflow-hidden rounded-md bg-white/20 px-4 font-bold shadow-xl backdrop-blur-md transition-all before:absolute before:top-0 before:left-0 before:h-full before:w-[3px] before:rounded-l-md before:bg-blue-500/90 hover:bg-white/30 disabled:cursor-not-allowed disabled:opacity-50"
			>
				{#if isLoading}
					<Icon icon="eos-icons:loading" class="animate-spin text-lg" />
				{:else}
					<Icon icon="material-symbols:send-rounded" class="text-lg" />
				{/if}
			</button>
		</div>
	</div>

	<div class="space-y-3">
		<h3 class="text-sm font-semibold text-gray-700">
			{globals.texts().smartOverview.examplesTitle}
		</h3>
		<div class="grid gap-2">
			{#each Object.values(globals.texts().smartOverview.exampleQuestions) as question}
				<button
					onclick={() => handleExampleClick(question)}
					class="font-heading relative w-full cursor-pointer overflow-hidden rounded-md border border-white/20 bg-white/10 p-3 text-left text-sm font-medium text-gray-700 shadow-md backdrop-blur-md transition-all before:absolute before:top-0 before:left-0 before:h-full before:w-[2px] before:rounded-l-md before:bg-gray-400/60 hover:bg-white/20 hover:shadow-lg hover:before:bg-blue-500/80"
				>
					{question}
				</button>
			{/each}
		</div>
	</div>

	<div class="rounded-lg border border-white/20 bg-white/10 backdrop-blur-md">
		<div class="flex items-center gap-2 border-b border-white/10 p-4 text-gray-600">
			<Icon icon="streamline-flex:ai-scanner-robot-solid" class="text-lg" />
			<span class="text-sm font-medium">{globals.texts().smartOverview.responseText}</span>
		</div>

		<div class="max-h-96 overflow-y-auto p-4">
			{#if isLoading}
				<div class="flex items-center justify-center py-8">
					<Icon icon="eos-icons:loading" class="animate-spin text-2xl text-gray-500" />
					<span class="ml-2 text-sm text-gray-500">Gerando resposta...</span>
				</div>
			{:else if error}
				<div class="rounded-md bg-red-50/80 p-3 text-red-700">
					<div class="flex items-center gap-2">
						<Icon icon="material-symbols:error-outline" class="text-lg" />
						<span class="text-sm font-medium">Erro:</span>
					</div>
					<p class="mt-1 text-sm">{error}</p>
				</div>
			{:else if response}
				<div class="prose prose-sm max-w-none text-gray-700">
					{@html response
						.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
						.replace(/\*(.*?)\*/g, '<em>$1</em>')
						.replace(/`(.*?)`/g, '<code class="bg-gray-200 px-1 py-0.5 rounded text-xs">$1</code>')
						.replace(/^### (.*$)/gim, '<h3 class="text-base font-semibold mt-4 mb-2">$1</h3>')
						.replace(/^## (.*$)/gim, '<h2 class="text-lg font-semibold mt-4 mb-2">$1</h2>')
						.replace(/^# (.*$)/gim, '<h1 class="text-xl font-bold mt-4 mb-2">$1</h1>')
						.replace(/^\- (.*$)/gim, '<li class="ml-4">$1</li>')
						.replace(/\n\n/g, '</p><p class="mt-2">')
						.replace(/^(?!<[h|l|c])/gm, '<p>')
						.replace(/(?<!>)$/gm, '</p>')
						.replace(/<p><\/p>/g, '')
						.replace(/<p>(<[h|l])/g, '$1')
						.replace(/(<\/[h|l][^>]*>)<\/p>/g, '$1')}
				</div>
			{:else}
				<div class="py-8 text-center text-gray-500">
					<Icon icon="material-symbols:chat-bubble-outline" class="mx-auto text-3xl" />
					<p class="mt-2 text-sm">Faça uma pergunta para começar</p>
				</div>
			{/if}
		</div>
	</div>
</div>
