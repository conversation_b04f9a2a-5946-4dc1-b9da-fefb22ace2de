<script lang="ts">
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import { gsap } from 'gsap';
	import { globals } from '$lib/states/global.state.svelte';

	let hoveredProject: number | null = null;
	let videoElements: { [key: number]: HTMLVideoElement } = {};
	let videoReady: { [key: number]: boolean } = {};

	const projects = [
		{
			id: 1,
			title: globals.texts().projectsList.projects.soraizReservas.title,
			description: globals.texts().projectsList.projects.soraizReservas.description,
			image: 'https://ik.imagekit.io/97q72hphb/consultancy.png?updatedAt=1731595663230',
			link: 'https://soraizreservas.com',
			video: 'https://ik.imagekit.io/97q72hphb/soraiz-exemplo.mp4?updatedAt=1751560433872'
		},
		{
			id: 2,
			title: globals.texts().projectsList.projects.eventia.title,
			description: globals.texts().projectsList.projects.eventia.description,
			image: 'https://ik.imagekit.io/97q72hphb/default-event-image.png?updatedAt=1745724246994',
			link: 'https://placeholder-eventia.com',
			video: 'https://ik.imagekit.io/97q72hphb/eventia-exemplo.mp4?updatedAt=1751560628673'
		},
		{
			id: 3,
			title: globals.texts().projectsList.projects.ibiCash.title,
			description: globals.texts().projectsList.projects.ibiCash.description,
			video: 'https://ik.imagekit.io/97q72hphb/ibicash-exemplo.mp4?updatedAt=1751563595237',
			image: 'https://cdn.landing.love/images/ibi-01.webp',
			link: 'https://ibi.cash'
		},
		{
			id: 4,
			title: globals.texts().projectsList.projects.midasGestor.title,
			description: globals.texts().projectsList.projects.midasGestor.description,
			image:
				'https://abar.org.br/wp-content/uploads/2024/01/Formac%CC%A7a%CC%83o-de-gestor-de-contratos.jpg'
		},
		{
			id: 5,
			title: globals.texts().projectsList.projects.cryptoTracker.title,
			description: globals.texts().projectsList.projects.cryptoTracker.description,
			image:
				'https://png.pngtree.com/thumb_back/fh260/background/20240715/pngtree-crypto-trade-btc-bitcoin-illustration-for-graphics-marketing-promotion-image_16011614.jpg'
		},
		{
			id: 6,
			title: globals.texts().projectsList.projects.regenteApp.title,
			description: globals.texts().projectsList.projects.regenteApp.description,
			image: 'https://ik.imagekit.io/og7loqgh2/regente-logo.png?updatedAt=1747848976333'
		}
	];

	function handleMouseEnter(project: any) {
		hoveredProject = project.id;

		if (project.video && videoElements[project.id] && videoReady[project.id]) {
			videoElements[project.id].currentTime = 0;
			videoElements[project.id].play().catch(() => {
				// Silently handle autoplay errors
			});
		}
	}

	function handleMouseLeave(project: any) {
		hoveredProject = null;

		if (project.video && videoElements[project.id]) {
			videoElements[project.id].pause();
			videoElements[project.id].currentTime = 0;
		}
	}

	function handleClick(project: any) {
		if (project.link) {
			window.open(project.link, '_blank');
		}
	}

	onMount(() => {
		gsap.fromTo(
			'.project-card',
			{
				opacity: 0,
				y: 50,
				scale: 0.9
			},
			{
				opacity: 1,
				y: 0,
				scale: 1,
				duration: 0.6,
				ease: 'power2.out',
				stagger: 0.1
			}
		);
	});
</script>

<div class="h-fit w-full" in:fade>
	<!-- Avatar/Imagem centralizada -->
	<div class="mt-6 flex justify-center">
		<div
			class="size-32 overflow-hidden rounded-full border-2 border-white/20 bg-white/10 shadow-xl backdrop-blur-md"
		>
			<img
				src="https://ik.imagekit.io/og7loqgh2/analyze.png?updatedAt=1751407038219"
				alt={globals.texts().projectsList.imageAlt}
				class="h-full w-full object-cover"
			/>
		</div>
	</div>

	<!-- Texto explicativo -->
	<div class="mt-3 text-center">
		<p class="mx-auto max-w-lg text-sm text-gray-800">
			{globals.texts().projectsList.description}
		</p>
	</div>

	<!-- Grid de projetos -->
	<div class="mt-6 h-[500px] overflow-x-hidden overflow-y-auto rounded-xl bg-white p-6">
		<div class="flex flex-col gap-6">
			{#each projects as project (project.id)}
				<!-- svelte-ignore a11y_no_noninteractive_tabindex -->
				<div
					class="project-card overflow-hidden rounded-xl border border-gray-300 bg-white shadow-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-xl {project.link
						? 'cursor-pointer'
						: ''}"
					on:mouseenter={() => handleMouseEnter(project)}
					on:mouseleave={() => handleMouseLeave(project)}
					on:click={() => handleClick(project)}
					role={project.link ? 'button' : 'article'}
					tabindex={project.link ? 0 : undefined}
				>
					<!-- Imagem/Vídeo do projeto -->
					<div class="relative h-[80px] overflow-hidden">
						<!-- Imagem base -->
						<img
							src={project.image}
							alt={project.title}
							class="h-full w-full object-cover transition-opacity duration-300"
							style="opacity: {hoveredProject === project.id && project.video ? 0 : 1}"
						/>

						<!-- Vídeo de preview (se existir) -->
						{#if project.video}
							<video
								bind:this={videoElements[project.id]}
								class="absolute inset-0 h-full w-full object-cover transition-opacity duration-300"
								style="opacity: {hoveredProject === project.id && videoReady[project.id] ? 1 : 0}"
								muted
								loop
								playsinline
								disablePictureInPicture
								preload="metadata"
								on:loadeddata={() => (videoReady[project.id] = true)}
								on:contextmenu|preventDefault
							>
								<source src={project.video} type="video/mp4" />
							</video>
						{/if}

						<!-- Overlay para projetos clicáveis -->
						{#if project.link}
							<div
								class="absolute inset-0 bg-black/0 transition-colors duration-300 hover:bg-black/5"
							></div>
						{/if}
					</div>

					<!-- Conteúdo do card -->
					<div class="p-4">
						<!-- Título -->
						<h3 class="mb-2 text-lg font-bold text-black">
							{project.title}
						</h3>

						<!-- Descrição -->
						<p class="text-sm leading-relaxed text-gray-700">
							{project.description}
						</p>

						<!-- Indicador de link (se existir) -->
						{#if project.link}
							<div class="mt-3 flex items-center text-xs text-blue-600">
								<svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
									></path>
								</svg>
								Ver projeto
							</div>
						{/if}
					</div>
				</div>
			{/each}
		</div>
	</div>
</div>
